# 🛠️ Gerenciador de Mudanças Locais

Scripts para gerenciar ajustes locais de desenvolvimento que **NÃO devem ser commitados**.

## 🎯 Problema Resolvido

Durante o desenvolvimento, você precisa de ajustes locais para:
- ✅ Hot reload funcional
- ✅ Correção de dependências circulares  
- ✅ Melhorias de performance
- ✅ Configurações específicas de desenvolvimento

**Mas esses ajustes NÃO devem ir para o repositório!**

## 📁 Arquivos Gerenciados

Os scripts gerenciam automaticamente estes arquivos:

### **Configurações:**
- `package.json`
- `webpack.config.js`
- `CVP-PlataformaCaixa-PosVenda-Previdencia.tsx`

### **Correções de Dependências Circulares:**
- `src/corporativo/constants/transferencias/constants/FluxoDeTransferencia.ts`
- `src/features/financeiro/transferencias/constants/index.ts`
- `src/features/financeiro/transferencias/exports/index.ts`
- `src/features/financeiro/transferencias/transferenciaEntreFundos/exports/index.ts`

### **Views Corrigidas:**
- `src/features/financeiro/transferencias/transferenciaEntreFundos/views/AlertasTransferenciaCases.tsx`
- `src/features/financeiro/transferencias/transferenciaEntreFundos/views/FundosLayout.tsx`
- `src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/*.tsx`

### **Utilitários de Desenvolvimento:**
- `src/utils/hmr-helper.ts`
- `src/utils/hmr-setup.ts`
- `src/utils/react-fast-refresh-helper.ts`

## 🚀 Como Usar

### **Windows (PowerShell):**
```powershell
# Fazer backup dos ajustes atuais
.\scripts\manage-local-changes.ps1 backup

# Ver status dos arquivos
.\scripts\manage-local-changes.ps1 status

# Restaurar ajustes do backup
.\scripts\manage-local-changes.ps1 restore

# Limpar todas as mudanças locais (cuidado!)
.\scripts\manage-local-changes.ps1 clean

# Ver ajuda
.\scripts\manage-local-changes.ps1 help
```

### **Linux/Mac (Bash):**
```bash
# Fazer backup dos ajustes atuais
./scripts/manage-local-changes.sh backup

# Ver status dos arquivos
./scripts/manage-local-changes.sh status

# Restaurar ajustes do backup
./scripts/manage-local-changes.sh restore

# Limpar todas as mudanças locais (cuidado!)
./scripts/manage-local-changes.sh clean

# Ver ajuda
./scripts/manage-local-changes.sh help
```

## 📋 Workflow Recomendado

### **1. Setup Inicial:**
```bash
# Aplicar seus ajustes locais
git stash pop  # ou fazer as correções manualmente

# Fazer backup
./scripts/manage-local-changes.sh backup

# Verificar que está funcionando
./scripts/manage-local-changes.sh status
```

### **2. Desenvolvimento Diário:**
```bash
# Manhã: Restaurar ajustes
./scripts/manage-local-changes.sh restore

# Trabalhar normalmente na sua demanda
git add feature.ts
git commit -m "feat: nova funcionalidade"

# Push sem os ajustes locais (eles são ignorados automaticamente)
git push origin sua-branch
```

### **3. Quando Precisar de Estado Limpo:**
```bash
# Fazer backup primeiro (segurança)
./scripts/manage-local-changes.sh backup

# Limpar para estado original
./scripts/manage-local-changes.sh clean

# Trabalhar com código limpo
# ...

# Restaurar ajustes quando terminar
./scripts/manage-local-changes.sh restore
```

## 🔧 Como Funciona

### **1. Git Exclude Local**
O arquivo `.git/info/exclude` funciona como um `.gitignore` local:
- ✅ Ignora mudanças nos arquivos listados
- ✅ Não é commitado (específico da sua máquina)
- ✅ Não afeta outros desenvolvedores

### **2. Backup Automático**
Os scripts criam backup em `.local-backup/`:
- ✅ Preserva estrutura de diretórios
- ✅ Permite restauração rápida
- ✅ Não é versionado pelo Git

### **3. Status Inteligente**
Mostra o estado atual:
- 🔄 Arquivos modificados
- ✅ Arquivos ignorados corretamente
- ❌ Arquivos não encontrados

## ⚠️ Importante

### **Arquivos Sempre Ignorados:**
Estes arquivos **NUNCA** aparecerão no `git status`:
- Suas correções de dependências circulares
- Ajustes de hot reload
- Configurações locais de desenvolvimento

### **Commits Limpos:**
Seus commits sempre estarão limpos, sem:
- ❌ Ajustes temporários
- ❌ Configurações específicas
- ❌ Correções que quebrariam outros devs

### **Segurança:**
- ✅ Backup automático antes de operações destrutivas
- ✅ Confirmação para operações perigosas
- ✅ Possibilidade de restaurar a qualquer momento

## 🆘 Troubleshooting

### **"Arquivo não encontrado"**
```bash
# Verificar se está no diretório correto
pwd
# Deve estar na raiz do projeto

# Verificar se scripts existem
ls scripts/
```

### **"Permission denied" (Linux/Mac)**
```bash
# Dar permissão de execução
chmod +x scripts/manage-local-changes.sh
```

### **"Backup não encontrado"**
```bash
# Fazer backup primeiro
./scripts/manage-local-changes.sh backup

# Depois restaurar
./scripts/manage-local-changes.sh restore
```

### **Git ainda mostra arquivos modificados**
```bash
# Verificar se exclude está configurado
cat .git/info/exclude

# Forçar atualização do Git
git update-index --skip-worktree arquivo.ts
```

## 💡 Dicas

1. **Sempre faça backup** antes de operações destrutivas
2. **Use `status`** para verificar se tudo está funcionando
3. **Mantenha backup atualizado** quando fizer novos ajustes
4. **Documente ajustes específicos** no seu time se necessário

---

**Criado para resolver problemas de dependências circulares e melhorar a experiência de desenvolvimento sem afetar o repositório principal.**
