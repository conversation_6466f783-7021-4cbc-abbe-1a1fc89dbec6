# Script para gerenciar mudanças locais de desenvolvimento
# Uso: .\scripts\manage-local-changes.ps1 [comando]

param(
    [Parameter(Position=0)]
    [ValidateSet("backup", "restore", "clean", "status", "help")]
    [string]$Command = "help"
)

$BackupDir = ".local-backup"
$ExcludeFile = ".git/info/exclude"

function Show-Help {
    Write-Host "=== Gerenciador de Mudanças Locais ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Comandos disponíveis:" -ForegroundColor Yellow
    Write-Host "  backup   - Faz backup dos arquivos locais" -ForegroundColor Green
    Write-Host "  restore  - Restaura arquivos do backup" -ForegroundColor Green  
    Write-Host "  clean    - Remove mudanças locais (volta ao estado original)" -ForegroundColor Red
    Write-Host "  status   - Mostra status dos arquivos ignorados" -ForegroundColor Blue
    Write-Host "  help     - Mostra esta ajuda" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "Exemplos:" -ForegroundColor Yellow
    Write-Host "  .\scripts\manage-local-changes.ps1 backup" -ForegroundColor Gray
    Write-Host "  .\scripts\manage-local-changes.ps1 restore" -ForegroundColor Gray
    Write-Host "  .\scripts\manage-local-changes.ps1 status" -ForegroundColor Gray
}

function Backup-LocalChanges {
    Write-Host "📦 Fazendo backup das mudanças locais..." -ForegroundColor Yellow
    
    if (!(Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir | Out-Null
    }
    
    # Lista de arquivos do exclude
    $files = @(
        "package.json",
        "webpack.config.js", 
        "CVP-PlataformaCaixa-PosVenda-Previdencia.tsx",
        "src/corporativo/constants/transferencias/constants/FluxoDeTransferencia.ts",
        "src/features/financeiro/transferencias/constants/index.ts",
        "src/features/financeiro/transferencias/exports/index.ts",
        "src/features/financeiro/transferencias/pages/Transferencia.tsx",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/exports/index.ts",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/types/TAlertasTransferenciaCases.ts",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/AlertasTransferenciaCases.tsx",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FundosLayout.tsx",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/DestinoAcoes.tsx",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/SucessoAcoes.tsx",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/ValidacaoAcoes.tsx",
        "src/utils/hmr-helper.ts",
        "src/utils/hmr-setup.ts", 
        "src/utils/react-fast-refresh-helper.ts",
        "docs/CIRCULAR_DEPENDENCIES_ANALYSIS.md"
    )
    
    foreach ($file in $files) {
        if (Test-Path $file) {
            $backupPath = Join-Path $BackupDir $file
            $backupDir = Split-Path $backupPath -Parent
            
            if (!(Test-Path $backupDir)) {
                New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
            }
            
            Copy-Item $file $backupPath -Force
            Write-Host "  ✅ $file" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  $file (não encontrado)" -ForegroundColor Yellow
        }
    }
    
    Write-Host "✅ Backup concluído em: $BackupDir" -ForegroundColor Green
}

function Restore-LocalChanges {
    Write-Host "📂 Restaurando mudanças locais..." -ForegroundColor Yellow
    
    if (!(Test-Path $BackupDir)) {
        Write-Host "❌ Backup não encontrado! Execute 'backup' primeiro." -ForegroundColor Red
        return
    }
    
    Get-ChildItem $BackupDir -Recurse -File | ForEach-Object {
        $relativePath = $_.FullName.Substring((Resolve-Path $BackupDir).Path.Length + 1)
        $targetPath = $relativePath
        
        $targetDir = Split-Path $targetPath -Parent
        if ($targetDir -and !(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        Copy-Item $_.FullName $targetPath -Force
        Write-Host "  ✅ $relativePath" -ForegroundColor Green
    }
    
    Write-Host "✅ Restauração concluída!" -ForegroundColor Green
}

function Clean-LocalChanges {
    Write-Host "🧹 Removendo mudanças locais..." -ForegroundColor Red
    Write-Host "⚠️  ATENÇÃO: Isso irá descartar todas as mudanças locais!" -ForegroundColor Yellow
    
    $confirmation = Read-Host "Tem certeza? (digite 'sim' para confirmar)"
    if ($confirmation -ne "sim") {
        Write-Host "❌ Operação cancelada." -ForegroundColor Yellow
        return
    }
    
    # Reset dos arquivos
    git checkout -- .
    
    Write-Host "✅ Mudanças locais removidas!" -ForegroundColor Green
    Write-Host "💡 Use 'restore' para recuperar do backup se necessário." -ForegroundColor Blue
}

function Show-Status {
    Write-Host "📊 Status dos arquivos ignorados:" -ForegroundColor Cyan
    Write-Host ""
    
    # Verificar se exclude existe
    if (Test-Path $ExcludeFile) {
        Write-Host "✅ Arquivo .git/info/exclude configurado" -ForegroundColor Green
    } else {
        Write-Host "❌ Arquivo .git/info/exclude não encontrado" -ForegroundColor Red
    }
    
    # Verificar backup
    if (Test-Path $BackupDir) {
        $backupFiles = (Get-ChildItem $BackupDir -Recurse -File).Count
        Write-Host "✅ Backup disponível ($backupFiles arquivos)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Nenhum backup encontrado" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "📁 Arquivos ignorados pelo Git:" -ForegroundColor Blue
    
    # Mostrar status dos arquivos
    $files = @(
        "package.json",
        "src/features/financeiro/transferencias/constants/index.ts",
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/AlertasTransferenciaCases.tsx"
    )
    
    foreach ($file in $files) {
        if (Test-Path $file) {
            $status = git status --porcelain $file 2>$null
            if ($status) {
                Write-Host "  🔄 $file (modificado)" -ForegroundColor Yellow
            } else {
                Write-Host "  ✅ $file (ignorado)" -ForegroundColor Green
            }
        } else {
            Write-Host "  ❌ $file (não existe)" -ForegroundColor Red
        }
    }
}

# Executar comando
switch ($Command) {
    "backup" { Backup-LocalChanges }
    "restore" { Restore-LocalChanges }
    "clean" { Clean-LocalChanges }
    "status" { Show-Status }
    "help" { Show-Help }
    default { Show-Help }
}
