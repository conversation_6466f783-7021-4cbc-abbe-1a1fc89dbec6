#!/bin/bash

# Script para gerenciar mudanças locais de desenvolvimento
# Uso: ./scripts/manage-local-changes.sh [comando]

BACKUP_DIR=".local-backup"
EXCLUDE_FILE=".git/info/exclude"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

show_help() {
    echo -e "${CYAN}=== Gerenciador de Mudanças Locais ===${NC}"
    echo ""
    echo -e "${YELLOW}Comandos disponíveis:${NC}"
    echo -e "  ${GREEN}backup${NC}   - Faz backup dos arquivos locais"
    echo -e "  ${GREEN}restore${NC}  - Restaura arquivos do backup"
    echo -e "  ${RED}clean${NC}    - Remove mudanças locais (volta ao estado original)"
    echo -e "  ${BLUE}status${NC}   - Mostra status dos arquivos ignorados"
    echo -e "  ${MAGENTA}help${NC}     - Mostra esta ajuda"
    echo ""
    echo -e "${YELLOW}Exemplos:${NC}"
    echo -e "  ${GRAY}./scripts/manage-local-changes.sh backup${NC}"
    echo -e "  ${GRAY}./scripts/manage-local-changes.sh restore${NC}"
    echo -e "  ${GRAY}./scripts/manage-local-changes.sh status${NC}"
}

backup_local_changes() {
    echo -e "${YELLOW}📦 Fazendo backup das mudanças locais...${NC}"
    
    mkdir -p "$BACKUP_DIR"
    
    # Lista de arquivos do exclude
    files=(
        "package.json"
        "webpack.config.js"
        "CVP-PlataformaCaixa-PosVenda-Previdencia.tsx"
        "src/corporativo/constants/transferencias/constants/FluxoDeTransferencia.ts"
        "src/features/financeiro/transferencias/constants/index.ts"
        "src/features/financeiro/transferencias/exports/index.ts"
        "src/features/financeiro/transferencias/pages/Transferencia.tsx"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/exports/index.ts"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/types/TAlertasTransferenciaCases.ts"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/AlertasTransferenciaCases.tsx"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FundosLayout.tsx"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/DestinoAcoes.tsx"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/SucessoAcoes.tsx"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/FooterAcoes/ValidacaoAcoes.tsx"
        "src/utils/hmr-helper.ts"
        "src/utils/hmr-setup.ts"
        "src/utils/react-fast-refresh-helper.ts"
        "docs/CIRCULAR_DEPENDENCIES_ANALYSIS.md"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            backup_path="$BACKUP_DIR/$file"
            backup_dir=$(dirname "$backup_path")
            
            mkdir -p "$backup_dir"
            cp "$file" "$backup_path"
            echo -e "  ${GREEN}✅ $file${NC}"
        else
            echo -e "  ${YELLOW}⚠️  $file (não encontrado)${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ Backup concluído em: $BACKUP_DIR${NC}"
}

restore_local_changes() {
    echo -e "${YELLOW}📂 Restaurando mudanças locais...${NC}"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        echo -e "${RED}❌ Backup não encontrado! Execute 'backup' primeiro.${NC}"
        return 1
    fi
    
    find "$BACKUP_DIR" -type f | while read -r backup_file; do
        relative_path=${backup_file#$BACKUP_DIR/}
        target_dir=$(dirname "$relative_path")
        
        if [ "$target_dir" != "." ] && [ ! -d "$target_dir" ]; then
            mkdir -p "$target_dir"
        fi
        
        cp "$backup_file" "$relative_path"
        echo -e "  ${GREEN}✅ $relative_path${NC}"
    done
    
    echo -e "${GREEN}✅ Restauração concluída!${NC}"
}

clean_local_changes() {
    echo -e "${RED}🧹 Removendo mudanças locais...${NC}"
    echo -e "${YELLOW}⚠️  ATENÇÃO: Isso irá descartar todas as mudanças locais!${NC}"
    
    read -p "Tem certeza? (digite 'sim' para confirmar): " confirmation
    if [ "$confirmation" != "sim" ]; then
        echo -e "${YELLOW}❌ Operação cancelada.${NC}"
        return 1
    fi
    
    # Reset dos arquivos
    git checkout -- .
    
    echo -e "${GREEN}✅ Mudanças locais removidas!${NC}"
    echo -e "${BLUE}💡 Use 'restore' para recuperar do backup se necessário.${NC}"
}

show_status() {
    echo -e "${CYAN}📊 Status dos arquivos ignorados:${NC}"
    echo ""
    
    # Verificar se exclude existe
    if [ -f "$EXCLUDE_FILE" ]; then
        echo -e "${GREEN}✅ Arquivo .git/info/exclude configurado${NC}"
    else
        echo -e "${RED}❌ Arquivo .git/info/exclude não encontrado${NC}"
    fi
    
    # Verificar backup
    if [ -d "$BACKUP_DIR" ]; then
        backup_count=$(find "$BACKUP_DIR" -type f | wc -l)
        echo -e "${GREEN}✅ Backup disponível ($backup_count arquivos)${NC}"
    else
        echo -e "${YELLOW}⚠️  Nenhum backup encontrado${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}📁 Arquivos ignorados pelo Git:${NC}"
    
    # Mostrar status dos arquivos principais
    files=(
        "package.json"
        "src/features/financeiro/transferencias/constants/index.ts"
        "src/features/financeiro/transferencias/transferenciaEntreFundos/views/AlertasTransferenciaCases.tsx"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            status=$(git status --porcelain "$file" 2>/dev/null)
            if [ -n "$status" ]; then
                echo -e "  ${YELLOW}🔄 $file (modificado)${NC}"
            else
                echo -e "  ${GREEN}✅ $file (ignorado)${NC}"
            fi
        else
            echo -e "  ${RED}❌ $file (não existe)${NC}"
        fi
    done
}

# Tornar script executável
chmod +x "$0" 2>/dev/null

# Executar comando
case "${1:-help}" in
    "backup")
        backup_local_changes
        ;;
    "restore")
        restore_local_changes
        ;;
    "clean")
        clean_local_changes
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        show_help
        ;;
esac
