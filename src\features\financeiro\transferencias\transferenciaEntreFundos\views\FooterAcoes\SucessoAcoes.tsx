import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import {
  Button,
  Grid,
  SwitchCase,
  Match,
  LoadingSpinner,
} from '@cvp/componentes-posvenda';
import { useTransferenciaContext } from '@src/corporativo/context/financeiro/transferencias';
import { useComprovanteTransferencia } from '@src/features/financeiro/transferencias/transferenciaEntreFundos/hook';
import { EErroTransferencia } from '@src/corporativo/types/transferencias';
import * as CONSTANTES from '@src/features/financeiro/transferencias/constants';

export const TransferenciaEntreFundosSucessoAcoes =
  (): React.ReactElement | null => {
    const { transferenciaRealizada, setErro } = useTransferenciaContext();
    const navigate = useNavigate();
    const { handleObterComprovante, loadingComprovantes, error, idRequisicao } =
      useComprovanteTransferencia();
    const theme = useTheme();

    if (!transferenciaRealizada) return null;

    if (error) {
      setErro(EErroTransferencia.ComprovanteTransferencia);
      return null;
    }

    return (
      <Grid gap="1rem" style={{ padding: '2rem' }} justify="flex-end">
        <Button
          variant="secondary"
          onClick={() => {
            handleObterComprovante();
          }}
        >
          <SwitchCase>
            <Match when={loadingComprovantes?.includes(idRequisicao)}>
              <LoadingSpinner
                size="small"
                color={theme.color.palette.grayscale['0']}
              />
            </Match>
            <Match when={!loadingComprovantes?.includes(idRequisicao)}>
              {CONSTANTES.BOTOES_SUCESSO.COMPROVANTE}
            </Match>
          </SwitchCase>
        </Button>

        <Button
          variant="primary"
          onClick={() => {
            navigate('/inicio');
          }}
        >
          {CONSTANTES.BOTOES_SUCESSO.FINALIZAR}
        </Button>
      </Grid>
    );
  };
