import React, { useContext } from 'react';
import { Button, Grid, GridItem } from '@cvp/componentes-posvenda';
import {
  useTransferenciaServicosContext,
  useTransferenciaContext,
} from '@src/corporativo/context/financeiro/transferencias';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import AssinaturaTransferencias from '@src/features/financeiro/transferencias/views/AssinaturaTransferencias';
import * as CONSTANTES from '@src/features/financeiro/transferencias/constants';

export const ValidacaoAcoes: React.FC = () => {
  const { consultaDestino, confirmarTransferencia } =
    useTransferenciaServicosContext();
  const { setTransferenciaRealizada, assinatura } = useTransferenciaContext();
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const confirmarValidacaoTransferencia = async () => {
    try {
      await confirmarTransferencia.fetchData({
        numeroCertificado: certificadoAtivo?.certificadoNumero ?? '',
        idTransferencia: consultaDestino.response.numTransferencia,
      });

      setTransferenciaRealizada(true);
    } catch (err) {
      setTransferenciaRealizada(false);
    }
  };

  return (
    <Grid justify="space-between">
      <GridItem xs="1/2">
        <AssinaturaTransferencias />
      </GridItem>
      <GridItem xs="1/9">
        <Button
          variant="secondary"
          onClick={confirmarValidacaoTransferencia}
          disabled={!!assinatura}
        >
          {CONSTANTES.SESSOES.FOOTER_BOTAO_PROSSEGUIR}
        </Button>
      </GridItem>
    </Grid>
  );
};
