/**
 * Helper para melhorar o Hot Module Replacement em Single-SPA
 */
import React from 'react';

declare global {
  interface NodeModule {
    hot?: {
      accept(path?: string, callback?: () => void): void;
      dispose(callback: () => void): void;
      data?: any;
    };
  }

  namespace NodeJS {
    interface Module {
      hot?: {
        accept(path?: string, callback?: () => void): void;
        dispose(callback: () => void): void;
        data?: any;
      };
    }
  }
}

export function enableHMR() {
  if (process.env.NODE_ENV === 'development' && module.hot) {
    // Aceita atualizações do módulo atual e de suas dependências
    module.hot.accept();

    // Limpa dados antigos quando o módulo é recarregado
    module.hot.dispose(() => {
      // Limpar listeners, timers, etc.
      console.log('🔄 HMR: Limpando módulo...');
    });

    return true;
  }
  return false;
}

export function isHMREnabled(): boolean {
  return process.env.NODE_ENV === 'development' && !!module.hot;
}

/**
 * Wrapper para componentes React que preserva estado durante HMR
 * Funciona com React Fast Refresh
 */
export function withHMR<T extends React.ComponentType<any>>(
  Component: T,
  displayName?: string,
): T {
  if (!isHMREnabled()) {
    return Component;
  }

  const WrappedComponent = React.memo((props: any) => {
    return React.createElement(Component, props);
  });

  WrappedComponent.displayName =
    displayName || Component.displayName || Component.name || 'HMRComponent';

  // Marca o componente para React Fast Refresh
  if (module.hot && typeof module.hot.accept === 'function') {
    (WrappedComponent as any)._hmrId =
      Component.name || displayName || 'anonymous';
  }

  return WrappedComponent as unknown as T;
}

/**
 * Hook para forçar re-render durante desenvolvimento
 */
export function useHMRUpdate(): () => void {
  const [, forceUpdate] = React.useReducer((x: number) => x + 1, 0);

  React.useEffect(() => {
    if (isHMREnabled() && module.hot) {
      const handleUpdate = () => {
        console.log('🔄 HMR: Forçando atualização do componente');
        forceUpdate();
      };

      module.hot.dispose(handleUpdate);

      return () => {
        // Cleanup se necessário
      };
    }
    return undefined;
  }, [forceUpdate]);

  return forceUpdate;
}
